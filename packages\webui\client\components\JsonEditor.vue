<template>
  <div class="json-editor">
    <k-card>
      <template #header>
        <div class="flex justify-between items-center">
          <h3>JSON 配置编辑器</h3>
          <div class="flex gap-2">
            <el-button size="small" @click="() => formatJson()" :disabled="!isValidJson">
              <k-icon name="align-left"></k-icon>
              格式化
            </el-button>
            <el-button size="small" @click="validateJson">
              <k-icon name="check"></k-icon>
              验证
            </el-button>
            <el-button size="small" @click="resetToDefault">
              <k-icon name="refresh-cw"></k-icon>
              重置
            </el-button>
            <el-button size="small" type="primary" @click="saveConfig" :disabled="!isValidJson">
              <k-icon name="save"></k-icon>
              保存
            </el-button>
          </div>
        </div>
      </template>

      <div class="json-editor-container"></div>
      <!-- 状态栏 -->
      <div class="status-bar">
        <div class="status-left">
          <span class="status-item" :class="{ 'status-error': !isValidJson, 'status-success': isValidJson }">
            <i :class="isValidJson ? 'icon-check-circle' : 'icon-x-circle'"></i>
            {{ isValidJson ? 'JSON 格式正确' : 'JSON 格式错误' }}
          </span>
          <span class="status-item">
            行: {{ currentLine }} 列: {{ currentColumn }}
          </span>
          <span class="status-item">
            字符数: {{ jsonContent.length }}
          </span>
        </div>
        <div class="status-right">
          <el-switch v-model="autoFormat" active-text="自动格式化" size="small" />
        </div>
      </div>

      <!-- 编辑器区域 -->
      <div class="editor-wrapper">
        <div class="line-numbers" v-if="showLineNumbers">
          <div v-for="(line, index) in lineCount" :key="index" class="line-number"
            :class="{ 'current-line': index + 1 === currentLine }">
            {{ index + 1 }}
          </div>
        </div>

        <textarea ref="editorTextarea" v-model="jsonContent" class="json-textarea"
          :class="{ 'has-error': !isValidJson }" @input="handleInput" @scroll="handleScroll"
          @click="updateCursorPosition" @keyup="updateCursorPosition" placeholder="请输入 JSON 配置..."
          spellcheck="false"></textarea>
      </div>

      <!-- 错误信息 -->
      <div v-if="validationError" class="error-panel">
        <div class="error-header">
          <i class="icon-alert-triangle"></i>
          JSON 语法错误
        </div>
        <div class="error-content">
          {{ validationError }}
        </div>
      </div>

      <!-- 配置模板快速插入 -->
      <div class="template-panel">
        <div class="template-header">
          <h4>配置模板</h4>
          <el-button size="small" @click="toggleTemplatePanel">
            {{ showTemplates ? '隐藏' : '显示' }}
          </el-button>
        </div>

        <div v-if="showTemplates" class="template-list">
          <div v-for="template in configTemplates" :key="template.key" class="template-item"
            @click="insertTemplate(template)">
            <div class="template-name">{{ template.name }}</div>
            <div class="template-desc">{{ template.description }}</div>
          </div>
        </div>
      </div>
    </k-card>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch, nextTick, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

// 响应式数据
const editorTextarea = ref<HTMLTextAreaElement>()
const jsonContent = ref<string>('')
const validationError = ref<string>('')
const currentLine = ref<number>(1)
const currentColumn = ref<number>(1)
const autoFormat = ref<boolean>(true)
const showLineNumbers = ref<boolean>(true)
const showTemplates = ref<boolean>(false)

// 计算属性
const isValidJson = computed(() => {
  if (!jsonContent.value.trim()) return true

  try {
    JSON.parse(jsonContent.value)
    validationError.value = ''
    return true
  } catch (error) {
    validationError.value = error instanceof Error ? error.message : '未知错误'
    return false
  }
})

const lineCount = computed(() => {
  return jsonContent.value.split('\n').length
})

// 配置模板
const configTemplates = ref([
  {
    key: 'modelService',
    name: '模型服务配置',
    description: '添加 AI 模型提供商和模型组配置',
    template: {
      modelService: {
        providers: [
          {
            name: "OpenAI",
            type: "OpenAI",
            baseURL: "https://api.openai.com/v1/",
            apiKey: "your-api-key",
            models: [
              {
                modelId: "gpt-4",
                abilities: ["对话", "函数调用"],
                parameters: {
                  temperature: 0.85,
                  topP: 0.95,
                  stream: true
                }
              }
            ]
          }
        ],
        modelGroups: [
          {
            name: "主要对话模型",
            strategy: "failover",
            models: [
              {
                providerName: "OpenAI",
                modelId: "gpt-4"
              }
            ]
          }
        ],
        task: {
          chat: "主要对话模型",
          embedding: "嵌入模型组"
        }
      }
    }
  },
  {
    key: 'agentBehavior',
    name: '智能体行为配置',
    description: '配置智能体的唤醒条件和响应行为',
    template: {
      agentBehavior: {
        arousal: {
          allowedChannels: [
            {
              platform: "onebot",
              type: "guild",
              id: "*"
            }
          ],
          debounceMs: 1000
        },
        willingness: {
          base: {
            text: 10
          },
          attribute: {
            atMention: 100,
            isQuote: 15,
            isDirectMessage: 40
          },
          interest: {
            keywords: [],
            keywordMultiplier: 1.2,
            defaultMultiplier: 1
          },
          lifecycle: {
            maxWillingness: 100,
            decayHalfLifeSeconds: 90,
            probabilityThreshold: 60,
            probabilityAmplifier: 0.05,
            replyCost: 30
          }
        },
        streamAction: false,
        heartbeat: 5
      }
    }
  },
  {
    key: 'capabilities',
    name: '能力配置',
    description: '配置记忆、历史记录和工具能力',
    template: {
      capabilities: {
        memory: {
          coreMemoryPath: "data/yesimbot/memory/core"
        },
        history: {
          l1_memory: {
            maxMessages: 50,
            pendingTurnTimeoutSec: 1800,
            keepFullTurnCount: 2
          },
          l2_memory: {
            enabled: true,
            retrievalK: 8,
            retrievalMinSimilarity: 0.55,
            messagesPerChunk: 4,
            includeNeighborChunks: true
          },
          ignoreSelfMessage: false,
          dataRetentionDays: 30,
          cleanupIntervalSec: 300
        },
        tools: {
          extra: {},
          advanced: {
            maxRetry: 3,
            retryDelay: 1000,
            timeout: 10000
          }
        }
      }
    }
  }
])

// 默认配置
const defaultConfig = {
  modelService: {
    providers: [],
    modelGroups: [],
    task: {
      chat: "",
      embedding: ""
    }
  },
  agentBehavior: {
    arousal: {
      allowedChannels: [],
      debounceMs: 1000
    },
    willingness: {},
    streamAction: false,
    heartbeat: 5
  },
  capabilities: {
    memory: {
      coreMemoryPath: "data/yesimbot/memory/core"
    },
    history: {},
    tools: {}
  },
  assetService: {
    storagePath: "data/assets",
    driver: "local"
  },
  promptService: {
    injectionPlaceholder: "extensions",
    maxRenderDepth: 3
  },
  system: {
    logging: {},
    errorReporting: {}
  }
}

// 方法
const handleInput = () => {
  if (autoFormat.value && isValidJson.value) {
    // 延迟格式化，避免输入时频繁格式化
    setTimeout(() => {
      if (isValidJson.value) {
        formatJson(false)
      }
    }, 1000)
  }
}

const handleScroll = () => {
  // 同步行号滚动
  const lineNumbers = document.querySelector('.line-numbers')
  if (lineNumbers && editorTextarea.value) {
    lineNumbers.scrollTop = editorTextarea.value.scrollTop
  }
}

const updateCursorPosition = () => {
  if (!editorTextarea.value) return

  const textarea = editorTextarea.value
  const cursorPos = textarea.selectionStart
  const textBeforeCursor = textarea.value.substring(0, cursorPos)
  const lines = textBeforeCursor.split('\n')

  currentLine.value = lines.length
  currentColumn.value = lines[lines.length - 1].length + 1
}

const formatJson = (showMessage = true) => {
  if (!isValidJson.value) {
    if (showMessage) {
      ElMessage.warning('JSON 格式错误，无法格式化')
    }
    return
  }

  try {
    const parsed = JSON.parse(jsonContent.value)
    jsonContent.value = JSON.stringify(parsed, null, 2)
    if (showMessage) {
      ElMessage.success('JSON 格式化完成')
    }
  } catch (error) {
    if (showMessage) {
      ElMessage.error('格式化失败')
    }
  }
}

const validateJson = () => {
  if (isValidJson.value) {
    ElMessage.success('JSON 格式正确')
  } else {
    ElMessage.error(`JSON 格式错误: ${validationError.value}`)
  }
}

const resetToDefault = () => {
  jsonContent.value = JSON.stringify(defaultConfig, null, 2)
  ElMessage.info('已重置为默认配置')
}

const saveConfig = () => {
  if (!isValidJson.value) {
    ElMessage.error('JSON 格式错误，无法保存')
    return
  }

  try {
    const config = JSON.parse(jsonContent.value)
    // 这里应该调用后端API保存配置
    console.log('Saving config:', config)
    ElMessage.success('配置保存成功')
  } catch (error) {
    ElMessage.error('保存失败')
  }
}

const toggleTemplatePanel = () => {
  showTemplates.value = !showTemplates.value
}

const insertTemplate = (template: any) => {
  try {
    let currentConfig = {}
    if (jsonContent.value.trim()) {
      currentConfig = JSON.parse(jsonContent.value)
    }

    const mergedConfig = { ...currentConfig, ...template.template }
    jsonContent.value = JSON.stringify(mergedConfig, null, 2)
    ElMessage.success(`已插入 ${template.name} 模板`)
  } catch (error) {
    ElMessage.error('插入模板失败，请检查当前 JSON 格式')
  }
}

// 初始化
onMounted(() => {
  // 加载当前配置（实际应该从后端获取）
  jsonContent.value = JSON.stringify(defaultConfig, null, 2)
})

// 监听内容变化，更新光标位置
watch(jsonContent, () => {
  nextTick(() => {
    updateCursorPosition()
  })
})
</script>

<style scoped>
.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.editor-actions {
  display: flex;
  gap: 0.5rem;
}

.json-editor-container {
  border: 1px solid var(--border-color);
  border-radius: 4px;
  overflow: hidden;
}

.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 1rem;
  background: var(--bg-color-secondary);
  border-bottom: 1px solid var(--border-color);
  font-size: 0.875rem;
}

.status-left {
  display: flex;
  gap: 1rem;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.status-success {
  color: var(--success-color);
}

.status-error {
  color: var(--error-color);
}

.editor-wrapper {
  display: flex;
  position: relative;
  min-height: 400px;
}

.line-numbers {
  background: var(--bg-color-tertiary);
  border-right: 1px solid var(--border-color);
  padding: 1rem 0.5rem;
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
  line-height: 1.5;
  color: var(--text-color-secondary);
  user-select: none;
  overflow: hidden;
  width: 3rem;
  text-align: right;
}

.line-number {
  height: 1.5em;
}

.line-number.current-line {
  background: var(--primary-color-light);
  color: var(--primary-color);
}

.json-textarea {
  flex: 1;
  border: none;
  outline: none;
  padding: 1rem;
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
  line-height: 1.5;
  background: transparent;
  color: var(--text-color);
  resize: none;
  min-height: 400px;
}

.json-textarea.has-error {
  background: var(--error-color-light);
}

.error-panel {
  background: var(--error-color-light);
  border-top: 1px solid var(--error-color);
  padding: 1rem;
}

.error-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  color: var(--error-color);
  margin-bottom: 0.5rem;
}

.error-content {
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
  color: var(--error-color);
}

.template-panel {
  border-top: 1px solid var(--border-color);
  background: var(--bg-color-secondary);
}

.template-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.template-list {
  max-height: 200px;
  overflow-y: auto;
}

.template-item {
  padding: 0.75rem 1rem;
  cursor: pointer;
  border-bottom: 1px solid var(--border-color);
  transition: background-color 0.2s;
}

.template-item:hover {
  background: var(--bg-color-tertiary);
}

.template-name {
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.template-desc {
  font-size: 0.875rem;
  color: var(--text-color-secondary);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .editor-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .status-bar {
    flex-direction: column;
    gap: 0.5rem;
    align-items: stretch;
  }

  .line-numbers {
    display: none;
  }
}
</style>
