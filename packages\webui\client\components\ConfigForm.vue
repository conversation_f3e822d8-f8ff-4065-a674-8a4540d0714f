<template>
  <k-card>
    <div class="config-form">
      <!-- 当前配置节的表单内容 -->
      <div class="form-content">
        <div class="section-header">
          <h3>{{ getCurrentSection()?.name }}</h3>
          <p class="section-description">{{ getCurrentSection()?.description }}</p>
        </div>

        <!-- 模型服务配置 -->
        <div v-if="activeSection === 'modelService'" class="form-section">
          <el-form :model="formData.modelService" label-width="120px">
            <!-- 提供商配置 -->
            <div class="subsection">
              <h5>AI 模型提供商</h5>
              <div v-for="(provider, index) in formData.modelService.providers" :key="index" class="provider-item">
                <el-card class="provider-card">
                  <template #header>
                    <div class="provider-header">
                      <span>提供商 {{ index + 1 }}</span>
                      <el-button size="small" type="danger" @click="removeProvider(index)">删除</el-button>
                    </div>
                  </template>

                  <el-form-item label="名称">
                    <el-input v-model="provider.name" placeholder="如: OpenAI" />
                  </el-form-item>

                  <el-form-item label="类型">
                    <el-select v-model="provider.type" placeholder="选择提供商类型">
                      <el-option v-for="type in providerTypes" :key="type" :label="type" :value="type" />
                    </el-select>
                  </el-form-item>

                  <el-form-item label="API 地址">
                    <el-input v-model="provider.baseURL" placeholder="https://api.openai.com/v1/" />
                  </el-form-item>

                  <el-form-item label="API 密钥">
                    <el-input v-model="provider.apiKey" type="password" show-password placeholder="输入 API 密钥" />
                  </el-form-item>

                  <el-form-item label="代理地址">
                    <el-input v-model="provider.proxy" placeholder="可选" />
                  </el-form-item>

                  <!-- 模型列表 -->
                  <div class="models-section">
                    <div class="models-header">
                      <label>模型列表</label>
                      <el-button size="small" @click="addModel(index)">添加模型</el-button>
                    </div>

                    <div v-for="(model, modelIndex) in provider.models" :key="modelIndex" class="model-item">
                      <el-form-item label="模型ID">
                        <el-input v-model="model.modelId" placeholder="如: gpt-4" />
                      </el-form-item>

                      <el-form-item label="支持能力">
                        <el-checkbox-group v-model="model.abilities">
                          <el-checkbox label="对话">对话</el-checkbox>
                          <el-checkbox label="视觉">视觉</el-checkbox>
                          <el-checkbox label="函数调用">函数调用</el-checkbox>
                          <el-checkbox label="嵌入">嵌入</el-checkbox>
                        </el-checkbox-group>
                      </el-form-item>

                      <el-form-item label="温度">
                        <el-slider v-model="model.parameters.temperature" :min="0" :max="2" :step="0.1" show-input />
                      </el-form-item>

                      <el-form-item label="Top P">
                        <el-slider v-model="model.parameters.topP" :min="0" :max="1" :step="0.05" show-input />
                      </el-form-item>

                      <el-button size="small" type="danger" @click="removeModel(index, modelIndex)">删除模型</el-button>
                    </div>
                  </div>
                </el-card>
              </div>

              <el-button @click="addProvider" type="primary">添加提供商</el-button>
            </div>

            <!-- 模型组配置 -->
            <div class="subsection">
              <h5>模型组配置</h5>
              <div v-for="(group, index) in formData.modelService.modelGroups" :key="index" class="group-item">
                <el-form-item label="组名称">
                  <el-input v-model="group.name" placeholder="如: 主要对话模型" />
                </el-form-item>

                <el-form-item label="切换策略">
                  <el-select v-model="group.strategy">
                    <el-option label="故障转移" value="failover" />
                    <el-option label="轮询" value="round-robin" />
                  </el-select>
                </el-form-item>

                <el-button size="small" type="danger" @click="removeModelGroup(index)">删除组</el-button>
              </div>

              <el-button @click="addModelGroup" type="primary">添加模型组</el-button>
            </div>
          </el-form>
        </div>

        <!-- 智能体行为配置 -->
        <div v-if="activeSection === 'agentBehavior'" class="form-section">
          <el-form :model="formData.agentBehavior" label-width="120px">
            <!-- 唤醒条件 -->
            <div class="subsection">
              <h5>唤醒条件</h5>
              <el-form-item label="防抖时间">
                <el-input-number v-model="formData.agentBehavior.arousal.debounceMs" :min="0" :step="100" />
                <span class="unit">毫秒</span>
              </el-form-item>

              <!-- 允许频道 -->
              <div class="channels-section">
                <label>允许响应的频道</label>
                <div v-for="(channel, index) in formData.agentBehavior.arousal.allowedChannels" :key="index"
                  class="channel-item">
                  <el-form-item label="平台">
                    <el-input v-model="channel.platform" placeholder="如: onebot" />
                  </el-form-item>

                  <el-form-item label="类型">
                    <el-select v-model="channel.type">
                      <el-option label="私聊" value="private" />
                      <el-option label="群组" value="guild" />
                    </el-select>
                  </el-form-item>

                  <el-form-item label="频道ID">
                    <el-input v-model="channel.id" placeholder="使用 * 作为通配符" />
                  </el-form-item>

                  <el-button size="small" type="danger" @click="removeChannel(index)">删除</el-button>
                </div>

                <el-button @click="addChannel" type="primary">添加频道</el-button>
              </div>
            </div>

            <!-- 响应意愿 -->
            <div class="subsection">
              <h5>响应意愿配置</h5>
              <el-form-item label="文本消息基础分">
                <el-input-number v-model="formData.agentBehavior.willingness.base.text" :min="0" />
              </el-form-item>

              <el-form-item label="@提及加成">
                <el-input-number v-model="formData.agentBehavior.willingness.attribute.atMention" :min="0" />
              </el-form-item>

              <el-form-item label="回复加成">
                <el-input-number v-model="formData.agentBehavior.willingness.attribute.isQuote" :min="0" />
              </el-form-item>

              <el-form-item label="私聊加成">
                <el-input-number v-model="formData.agentBehavior.willingness.attribute.isDirectMessage" :min="0" />
              </el-form-item>

              <el-form-item label="最大意愿值">
                <el-input-number v-model="formData.agentBehavior.willingness.lifecycle.maxWillingness" :min="10" />
              </el-form-item>

              <el-form-item label="衰减半衰期">
                <el-input-number v-model="formData.agentBehavior.willingness.lifecycle.decayHalfLifeSeconds" :min="5" />
                <span class="unit">秒</span>
              </el-form-item>
            </div>

            <!-- 其他行为设置 -->
            <div class="subsection">
              <h5>其他设置</h5>
              <el-form-item label="流式响应">
                <el-switch v-model="formData.agentBehavior.streamAction" />
              </el-form-item>

              <el-form-item label="心跳次数">
                <el-slider v-model="formData.agentBehavior.heartbeat" :min="1" :max="10" :step="1" show-input />
              </el-form-item>

              <el-form-item label="新消息策略">
                <el-select v-model="formData.agentBehavior.newMessageStrategy">
                  <el-option label="跳过新消息" value="skip" />
                  <el-option label="立即处理新消息" value="immediate" />
                  <el-option label="延迟处理被跳过话题" value="deferred" />
                </el-select>
              </el-form-item>
            </div>
          </el-form>
        </div>

        <!-- 能力配置 -->
        <div v-if="activeSection === 'capabilities'" class="form-section">
          <el-form :model="formData.capabilities" label-width="120px">
            <!-- 记忆配置 -->
            <div class="subsection">
              <h5>记忆配置</h5>
              <el-form-item label="核心记忆路径">
                <el-input v-model="formData.capabilities.memory.coreMemoryPath"
                  placeholder="data/yesimbot/memory/core" />
              </el-form-item>
            </div>

            <!-- 历史记录配置 -->
            <div class="subsection">
              <h5>历史记录配置</h5>
              <el-form-item label="最大消息数">
                <el-input-number v-model="formData.capabilities.history.l1_memory.maxMessages" :min="10" />
              </el-form-item>

              <el-form-item label="启用语义检索">
                <el-switch v-model="formData.capabilities.history.l2_memory.enabled" />
              </el-form-item>

              <el-form-item label="检索片段数">
                <el-input-number v-model="formData.capabilities.history.l2_memory.retrievalK" :min="1" />
              </el-form-item>

              <el-form-item label="相似度阈值">
                <el-slider v-model="formData.capabilities.history.l2_memory.retrievalMinSimilarity" :min="0" :max="1"
                  :step="0.05" show-input />
              </el-form-item>
            </div>

            <!-- 工具配置 -->
            <div class="subsection">
              <h5>工具配置</h5>
              <el-form-item label="最大重试次数">
                <el-input-number v-model="formData.capabilities.tools.advanced.maxRetry" :min="0" />
              </el-form-item>

              <el-form-item label="重试延迟">
                <el-input-number v-model="formData.capabilities.tools.advanced.retryDelay" :min="0" :step="100" />
                <span class="unit">毫秒</span>
              </el-form-item>

              <el-form-item label="超时时间">
                <el-input-number v-model="formData.capabilities.tools.advanced.timeout" :min="1000" :step="1000" />
                <span class="unit">毫秒</span>
              </el-form-item>
            </div>
          </el-form>
        </div>

        <!-- 其他配置节 -->
        <div v-if="activeSection === 'assetService'" class="form-section">
          <el-form :model="formData.assetService" label-width="120px">
            <el-form-item label="存储路径">
              <el-input v-model="formData.assetService.storagePath" placeholder="data/assets" />
            </el-form-item>

            <el-form-item label="存储驱动">
              <el-select v-model="formData.assetService.driver">
                <el-option label="本地存储" value="local" />
              </el-select>
            </el-form-item>

            <el-form-item label="最大文件大小">
              <el-input-number v-model="formData.assetService.maxFileSize" :min="1" />
              <span class="unit">MB</span>
            </el-form-item>
          </el-form>
        </div>

        <div v-if="activeSection === 'promptService'" class="form-section">
          <el-form :model="formData.promptService" label-width="120px">
            <el-form-item label="注入占位符">
              <el-input v-model="formData.promptService.injectionPlaceholder" placeholder="extensions" />
            </el-form-item>

            <el-form-item label="最大渲染深度">
              <el-input-number v-model="formData.promptService.maxRenderDepth" :min="1" />
            </el-form-item>
          </el-form>
        </div>

        <div v-if="activeSection === 'system'" class="form-section">
          <el-form :model="formData.system" label-width="120px">
            <div class="subsection">
              <h5>系统配置</h5>
              <p>系统级配置选项将在后续版本中提供更多设置。</p>
            </div>
          </el-form>
        </div>
      </div>
    </div>
  </k-card>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'

// Props
interface Props {
  config: any
  activeSection?: string
}

const props = withDefaults(defineProps<Props>(), {
  activeSection: 'modelService'
})

// Emits
const emit = defineEmits<{
  'config-changed': [config: any]
  'config-saved': []
  'section-changed': [section: string]
}>()

// 配置分类
const configSections = [
  { key: 'modelService', name: '模型服务', icon: 'cpu', description: 'AI 模型、API密钥和模型组配置' },
  { key: 'agentBehavior', name: '智能体行为', icon: 'user', description: '智能体的性格、唤醒和响应逻辑' },
  { key: 'capabilities', name: '能力配置', icon: 'zap', description: '记忆、工具等扩展能力配置' },
  { key: 'assetService', name: '资源服务', icon: 'folder', description: '资源服务配置' },
  { key: 'promptService', name: '提示词服务', icon: 'message-square', description: '提示词相关配置' },
  { key: 'system', name: '系统配置', icon: 'settings', description: '系统缓存、调试等底层设置' }
]

const providerTypes = [
  'OpenAI', 'OpenAI Compatible', 'Anthropic', 'Fireworks', 'DeepSeek',
  'Google Gemini', 'LM Studio', 'Workers AI', 'Zhipu', 'Silicon Flow',
  'Qwen', 'Ollama', 'Cerebras', 'DeepInfra', 'Groq', 'Minimax', 'Mistral'
]

// 响应式数据
const activeSection = ref(props.activeSection)
const formErrors = ref<Record<string, string[]>>({})

// 表单数据
const formData = ref({
  modelService: {
    providers: [],
    modelGroups: [],
    task: { chat: '', embedding: '' }
  },
  agentBehavior: {
    arousal: {
      allowedChannels: [{ platform: 'onebot', type: 'guild', id: '*' }],
      debounceMs: 1000
    },
    willingness: {
      base: { text: 10 },
      attribute: { atMention: 100, isQuote: 15, isDirectMessage: 40 },
      lifecycle: { maxWillingness: 100, decayHalfLifeSeconds: 90 }
    },
    streamAction: false,
    heartbeat: 5,
    newMessageStrategy: 'skip'
  },
  capabilities: {
    memory: { coreMemoryPath: 'data/yesimbot/memory/core' },
    history: {
      l1_memory: { maxMessages: 50 },
      l2_memory: { enabled: true, retrievalK: 8, retrievalMinSimilarity: 0.55 }
    },
    tools: {
      advanced: { maxRetry: 3, retryDelay: 1000, timeout: 10000 }
    }
  },
  assetService: {
    storagePath: 'data/assets',
    driver: 'local',
    maxFileSize: 100
  },
  promptService: {
    injectionPlaceholder: 'extensions',
    maxRenderDepth: 3
  },
  system: {
    logging: {},
    errorReporting: {}
  }
})

// 计算属性
const getCurrentSection = () => {
  return configSections.find(section => section.key === activeSection.value)
}

const getSectionErrors = (sectionKey: string) => {
  return formErrors.value[sectionKey] || []
}

// 方法
const addProvider = () => {
  formData.value.modelService.providers.push({
    name: '',
    type: 'OpenAI',
    baseURL: '',
    apiKey: '',
    proxy: '',
    models: []
  })
}

const removeProvider = (index: number) => {
  formData.value.modelService.providers.splice(index, 1)
}

const addModel = (providerIndex: number) => {
  formData.value.modelService.providers[providerIndex].models.push({
    modelId: '',
    abilities: ['对话'],
    parameters: { temperature: 0.85, topP: 0.95, stream: true }
  })
}

const removeModel = (providerIndex: number, modelIndex: number) => {
  formData.value.modelService.providers[providerIndex].models.splice(modelIndex, 1)
}

const addModelGroup = () => {
  formData.value.modelService.modelGroups.push({
    name: '',
    strategy: 'failover',
    models: []
  })
}

const removeModelGroup = (index: number) => {
  formData.value.modelService.modelGroups.splice(index, 1)
}

const addChannel = () => {
  formData.value.agentBehavior.arousal.allowedChannels.push({
    platform: 'onebot',
    type: 'guild',
    id: '*'
  })
}

const removeChannel = (index: number) => {
  formData.value.agentBehavior.arousal.allowedChannels.splice(index, 1)
}

const resetForm = () => {
  // 重置表单数据
  ElMessage.info('表单已重置')
}

const validateForm = () => {
  // 验证表单
  formErrors.value = {}
  ElMessage.success('表单验证通过')
}

const saveForm = () => {
  // 保存表单数据
  console.log('Saving form data:', formData.value)
  ElMessage.success('配置保存成功')
}
</script>

<style lang="scss">
.config-form {
  .form-content {
    max-width: none;
  }

  .section-header {
    margin-bottom: 2rem;

    h3 {
      margin: 0 0 0.5rem 0;
      color: var(--fg1);
    }

    .section-description {
      color: var(--fg2);
      margin: 0;
      font-size: 0.875rem;
    }
  }

  .form-section {
    .subsection {
      margin-bottom: 2rem;
      padding: 1.5rem;
      border: 1px solid var(--border);
      border-radius: 0.5rem;
      background: var(--bg1);

      h5 {
        margin: 0 0 1rem 0;
        color: var(--fg1);
        font-size: 1rem;
        font-weight: 600;
      }
    }

    .provider-card {
      margin-bottom: 1rem;

      .provider-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
    }

    .models-section {
      margin-top: 1rem;

      .models-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;

        label {
          font-weight: 500;
          color: var(--fg1);
        }
      }

      .model-item {
        padding: 1rem;
        border: 1px solid var(--border);
        border-radius: 0.375rem;
        margin-bottom: 1rem;
        background: var(--bg2);
      }
    }

    .channels-section {
      .channel-item {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr auto;
        gap: 1rem;
        align-items: end;
        padding: 1rem;
        border: 1px solid var(--border);
        border-radius: 0.375rem;
        margin-bottom: 1rem;
        background: var(--bg2);
      }
    }

    .unit {
      margin-left: 0.5rem;
      color: var(--fg2);
      font-size: 0.875rem;
    }
  }
}

// Element Plus 组件样式覆盖
.el-form-item {
  margin-bottom: 1.5rem;
}

.el-form-item__label {
  color: var(--fg1);
  font-weight: 500;
}

.el-input,
.el-select,
.el-input-number {
  .el-input__wrapper {
    background-color: var(--bg1);
    border-color: var(--border);

    &:hover {
      border-color: var(--primary);
    }

    &.is-focus {
      border-color: var(--primary);
      box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.2);
    }
  }
}

.el-checkbox-group {
  .el-checkbox {
    margin-right: 1rem;
    margin-bottom: 0.5rem;

    .el-checkbox__label {
      color: var(--fg1);
    }
  }
}

.el-slider {
  .el-slider__runway {
    background-color: var(--bg2);
  }

  .el-slider__bar {
    background-color: var(--primary);
  }

  .el-slider__button {
    border-color: var(--primary);
  }
}

@media (max-width: 768px) {
  .config-form {
    .form-section {
      .channels-section {
        .channel-item {
          grid-template-columns: 1fr;
          gap: 0.5rem;
        }
      }
    }
  }
}
</style>
