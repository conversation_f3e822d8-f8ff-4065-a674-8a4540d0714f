<template>
  <k-layout menu="config">
    <template #header>
      配置管理{{ currentView ? ' - ' + getCurrentViewName() : '' }}
    </template>

    <template #left>
      <el-scrollbar class="config-nav w-full h-full overflow-auto">
        <div class="nav-content">
          <!-- 视图切换 -->
          <div class="view-modes">
            <div class="nav-title">编辑模式</div>
            <div
              v-for="view in viewModes"
              :key="view.key"
              class="nav-item"
              :class="{ active: currentView === view.key }"
              @click="currentView = view.key"
            >
              <k-icon :name="view.icon"></k-icon>
              <span>{{ view.name }}</span>
            </div>
          </div>

          <!-- 配置分类 (仅在表单编辑模式下显示) -->
          <div v-if="currentView === 'form-editor'" class="config-sections">
            <div class="nav-title">配置分类</div>
            <div
              v-for="section in configSections"
              :key="section.key"
              class="nav-item"
              :class="{ active: activeSection === section.key }"
              @click="activeSection = section.key"
            >
              <k-icon :name="section.icon"></k-icon>
              <span>{{ section.name }}</span>
              <div v-if="getSectionErrors(section.key).length > 0" class="error-badge">
                {{ getSectionErrors(section.key).length }}
              </div>
            </div>
          </div>

          <!-- 状态信息 -->
          <div class="status-section">
            <div class="nav-title">状态信息</div>
            <div class="status-list">
              <div class="status-item" :class="{ 'text-success': configValid, 'text-danger': !configValid }">
                <k-icon :name="configValid ? 'check-circle' : 'alert-circle'"></k-icon>
                <span>{{ configValid ? '配置有效' : '配置错误' }}</span>
              </div>

              <div class="status-item">
                <k-icon name="clock"></k-icon>
                <span>{{ lastSaved || '未保存' }}</span>
              </div>

              <div class="status-item">
                <k-icon name="database"></k-icon>
                <span>v{{ configVersion }}</span>
              </div>
            </div>
          </div>
        </div>
      </el-scrollbar>
    </template>

    <!-- 主内容区域 -->
    <k-content v-if="currentView">
      <!-- 导入导出视图 -->
      <div v-if="currentView === 'import-export'">
        <ConfigImportExport @config-imported="handleConfigImported" />
      </div>

      <!-- JSON 编辑器视图 -->
      <div v-if="currentView === 'json-editor'">
        <JsonEditor
          :initial-config="currentConfig"
          @config-changed="handleConfigChanged"
          @config-saved="handleConfigSaved"
        />
      </div>

      <!-- 表单编辑器视图 -->
      <div v-if="currentView === 'form-editor'">
        <ConfigForm
          :config="currentConfig"
          :active-section="activeSection"
          @config-changed="handleConfigChanged"
          @config-saved="handleConfigSaved"
          @section-changed="activeSection = $event"
        />
      </div>

      <!-- 配置预览视图 -->
      <div v-if="currentView === 'preview'">
        <k-card>
          <template #header>
            <div class="flex justify-between items-center">
              <h3>配置预览</h3>
              <div class="flex gap-2">
                <el-button size="small" @click="refreshPreview">
                  <k-icon name="refresh-cw"></k-icon>
                  刷新
                </el-button>
                <el-button size="small" @click="exportPreview">
                  <k-icon name="download"></k-icon>
                  导出
                </el-button>
              </div>
            </div>
          </template>

          <!-- 配置概览 -->
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            <div class="bg-gray-50 p-4 rounded-lg text-center">
              <div class="text-2xl font-bold text-blue-600">{{ getProviderCount() }}</div>
              <div class="text-sm text-gray-600">模型提供商</div>
            </div>
            <div class="bg-gray-50 p-4 rounded-lg text-center">
              <div class="text-2xl font-bold text-green-600">{{ getModelCount() }}</div>
              <div class="text-sm text-gray-600">配置的模型</div>
            </div>
            <div class="bg-gray-50 p-4 rounded-lg text-center">
              <div class="text-2xl font-bold text-purple-600">{{ getModelGroupCount() }}</div>
              <div class="text-sm text-gray-600">模型组</div>
            </div>
            <div class="bg-gray-50 p-4 rounded-lg text-center">
              <div class="text-2xl font-bold text-orange-600">{{ getChannelCount() }}</div>
              <div class="text-sm text-gray-600">允许频道</div>
            </div>
          </div>

          <!-- 配置详情 -->
          <el-collapse v-model="activePreviewSections">
            <el-collapse-item
              v-for="section in previewSections"
              :key="section.key"
              :title="section.title"
              :name="section.key"
            >
              <pre class="bg-gray-100 p-4 rounded text-sm overflow-auto max-h-80">{{ formatSectionConfig(section.key) }}</pre>
            </el-collapse-item>
          </el-collapse>
        </k-card>
      </div>

      <!-- 底部操作栏 -->
      <div class="sticky bottom-0 bg-white border-t p-4 flex justify-between items-center mt-6">
        <div class="flex gap-2">
          <el-button @click="resetAllConfig" :disabled="!hasChanges">
            <k-icon name="refresh-cw"></k-icon>
            重置配置
          </el-button>

          <el-button @click="validateAllConfig">
            <k-icon name="check-square"></k-icon>
            验证配置
          </el-button>
        </div>

        <div class="flex gap-2">
          <el-button @click="discardChanges" :disabled="!hasChanges">
            取消更改
          </el-button>

          <el-button type="primary" @click="saveAllConfig" :disabled="!hasChanges || !configValid">
            <k-icon name="save"></k-icon>
            保存配置
          </el-button>
        </div>
      </div>
    </k-content>

    <k-empty v-else>
      <div>请在左侧选择编辑模式</div>
    </k-empty>
  </k-layout>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import ConfigImportExport from '../components/ConfigImportExport.vue'
import JsonEditor from '../components/JsonEditor.vue'
import ConfigForm from '../components/ConfigForm.vue'

// 视图模式
const viewModes = [
  { key: 'import-export', name: '导入导出', icon: 'upload-cloud' },
  { key: 'form-editor', name: '表单编辑', icon: 'edit-3' },
  { key: 'json-editor', name: 'JSON编辑', icon: 'code' },
  { key: 'preview', name: '配置预览', icon: 'eye' }
]

// 配置分类
const configSections = [
  { key: 'modelService', name: '模型服务', icon: 'cpu' },
  { key: 'agentBehavior', name: '智能体行为', icon: 'user' },
  { key: 'capabilities', name: '能力配置', icon: 'zap' },
  { key: 'assetService', name: '资源服务', icon: 'folder' },
  { key: 'promptService', name: '提示词服务', icon: 'message-square' },
  { key: 'system', name: '系统配置', icon: 'settings' }
]

// 预览部分
const previewSections = [
  { key: 'modelService', title: '模型服务配置' },
  { key: 'agentBehavior', title: '智能体行为配置' },
  { key: 'capabilities', title: '能力配置' },
  { key: 'assetService', title: '资源服务配置' },
  { key: 'promptService', title: '提示词服务配置' },
  { key: 'system', title: '系统配置' }
]

// 响应式数据
const currentView = ref('form-editor')
const activeSection = ref('modelService')
const configValid = ref(true)
const hasChanges = ref(false)
const lastSaved = ref<string>('')
const configVersion = ref('1.0.0')
const activePreviewSections = ref(['modelService', 'agentBehavior'])
const formErrors = ref<Record<string, string[]>>({})

// 当前配置数据
const currentConfig = ref({
  modelService: {
    providers: [],
    modelGroups: [],
    task: { chat: '', embedding: '' }
  },
  agentBehavior: {
    arousal: {
      allowedChannels: [{ platform: 'onebot', type: 'guild', id: '*' }],
      debounceMs: 1000
    },
    willingness: {
      base: { text: 10 },
      attribute: { atMention: 100, isQuote: 15, isDirectMessage: 40 },
      lifecycle: { maxWillingness: 100, decayHalfLifeSeconds: 90 }
    },
    streamAction: false,
    heartbeat: 5
  },
  capabilities: {
    memory: { coreMemoryPath: 'data/yesimbot/memory/core' },
    history: {
      l1_memory: { maxMessages: 50 },
      l2_memory: { enabled: true, retrievalK: 8, retrievalMinSimilarity: 0.55 }
    },
    tools: { advanced: { maxRetry: 3, retryDelay: 1000, timeout: 10000 } }
  },
  assetService: {
    storagePath: 'data/assets',
    driver: 'local',
    maxFileSize: 100
  },
  promptService: {
    injectionPlaceholder: 'extensions',
    maxRenderDepth: 3
  },
  system: {
    logging: {},
    errorReporting: {}
  }
})

// 计算属性
const getCurrentViewName = () => {
  return viewModes.find(view => view.key === currentView.value)?.name || ''
}

const getSectionErrors = (sectionKey: string) => {
  return formErrors.value[sectionKey] || []
}

const getProviderCount = () => {
  return currentConfig.value.modelService.providers?.length || 0
}

const getModelCount = () => {
  return currentConfig.value.modelService.providers?.reduce((count, provider) => {
    return count + (provider.models?.length || 0)
  }, 0) || 0
}

const getModelGroupCount = () => {
  return currentConfig.value.modelService.modelGroups?.length || 0
}

const getChannelCount = () => {
  return currentConfig.value.agentBehavior.arousal.allowedChannels?.length || 0
}

// 方法
const handleConfigImported = (config: any) => {
  currentConfig.value = { ...currentConfig.value, ...config }
  hasChanges.value = true
  ElMessage.success('配置已导入')
}

const handleConfigChanged = (config: any) => {
  currentConfig.value = config
  hasChanges.value = true
}

const handleConfigSaved = () => {
  hasChanges.value = false
  lastSaved.value = new Date().toLocaleString()
  ElMessage.success('配置已保存')
}

const refreshPreview = () => {
  ElMessage.info('预览已刷新')
}

const exportPreview = () => {
  const blob = new Blob([JSON.stringify(currentConfig.value, null, 2)], {
    type: 'application/json'
  })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `yesimbot-config-${Date.now()}.json`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
  ElMessage.success('配置已导出')
}

const formatSectionConfig = (sectionKey: string) => {
  const sectionConfig = currentConfig.value[sectionKey as keyof typeof currentConfig.value]
  return JSON.stringify(sectionConfig, null, 2)
}

const resetAllConfig = async () => {
  try {
    await ElMessageBox.confirm('确定要重置所有配置吗？此操作不可撤销。', '确认重置', {
      type: 'warning'
    })

    // 重置配置逻辑
    hasChanges.value = false
    ElMessage.success('配置已重置')
  } catch {
    // 用户取消
  }
}

const validateAllConfig = () => {
  // 配置验证逻辑
  configValid.value = true
  ElMessage.success('配置验证通过')
}

const discardChanges = async () => {
  try {
    await ElMessageBox.confirm('确定要放弃所有未保存的更改吗？', '确认放弃', {
      type: 'warning'
    })

    // 恢复配置逻辑
    hasChanges.value = false
    ElMessage.info('已放弃更改')
  } catch {
    // 用户取消
  }
}

const saveAllConfig = async () => {
  try {
    // 保存配置到后端的逻辑
    await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟API调用

    handleConfigSaved()
  } catch (error) {
    ElMessage.error('保存配置失败')
  }
}

// 初始化
onMounted(() => {
  // 加载当前配置
  lastSaved.value = new Date().toLocaleString()
})
</script>

<style lang="scss">
.config-nav {
  .el-scrollbar__view {
    padding: 1rem 0;
  }

  .nav-content {
    padding: 0 1rem;
  }

  .nav-title {
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    color: var(--fg2);
    margin: 1.5rem 0 0.5rem 0;

    &:first-child {
      margin-top: 0;
    }
  }

  .nav-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0.75rem;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 0.875rem;
    color: var(--fg1);
    position: relative;

    &:hover {
      background: var(--bg2);
    }

    &.active {
      background: var(--primary);
      color: white;
    }

    .error-badge {
      position: absolute;
      right: 0.5rem;
      background: var(--danger);
      color: white;
      border-radius: 50%;
      width: 1.25rem;
      height: 1.25rem;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 0.75rem;
      font-weight: 600;
    }
  }

  .status-list {
    .status-item {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.25rem 0.75rem;
      font-size: 0.75rem;
      color: var(--fg2);

      &.text-success {
        color: var(--success);
      }

      &.text-danger {
        color: var(--danger);
      }
    }
  }
}

// 使用 Tailwind 样式类，确保与 Koishi 主题兼容
.flex {
  display: flex;
}

.justify-between {
  justify-content: space-between;
}

.items-center {
  align-items: center;
}

.gap-2 {
  gap: 0.5rem;
}

.gap-4 {
  gap: 1rem;
}

.grid {
  display: grid;
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}

.p-4 {
  padding: 1rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.mt-6 {
  margin-top: 1.5rem;
}

.rounded-lg {
  border-radius: 0.5rem;
}

.rounded {
  border-radius: 0.25rem;
}

.text-center {
  text-align: center;
}

.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}

.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.font-bold {
  font-weight: 700;
}

.text-blue-600 {
  color: var(--primary);
}

.text-green-600 {
  color: var(--success);
}

.text-purple-600 {
  color: #9333ea;
}

.text-orange-600 {
  color: #ea580c;
}

.text-gray-600 {
  color: var(--fg2);
}

.bg-gray-50 {
  background-color: var(--bg2);
}

.bg-gray-100 {
  background-color: var(--bg2);
}

.bg-white {
  background-color: var(--bg1);
}

.border-t {
  border-top: 1px solid var(--border);
}

.sticky {
  position: sticky;
}

.bottom-0 {
  bottom: 0;
}

.overflow-auto {
  overflow: auto;
}

.max-h-80 {
  max-height: 20rem;
}

@media (min-width: 768px) {
  .md\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}
</style>
