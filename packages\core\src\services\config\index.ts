import { Services } from "@/shared/constants";
import { Context, Service } from "koishi";

import { Config as AppConfig } from "@/config";

declare module 'koishi' {
    interface Context {
        [Services.Config]: ConfigService;
    }
    interface Events {
        'config-updated'(path: string, value: any, oldValue: any): void;
    }
}

export class ConfigService extends Service<AppConfig> {
    constructor(ctx: Context, config: AppConfig) {
        super(ctx, Services.Config, true);
        this.config = config;
    }
}